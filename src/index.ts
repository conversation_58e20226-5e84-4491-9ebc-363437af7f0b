import { scrapeLinkedInPosts } from "./scraper";
import { analyzeWritingStyle } from "./analyzer";
import { WritingStyleProfile } from "./types";

async function main(url: string, email: string, password: string) {
	try {
		console.log(`Scraping LinkedIn profile: ${url}`);
		const posts = await scrapeLinkedInPosts(url, { email, password });

		if (posts.length === 0) {
			console.log("No posts found for analysis");
			return;
		}

		const analysis = analyzeWritingStyle(posts);
		console.log("\nWriting Style Analysis Report:");
		console.log("----------------------------");
		console.log(`Tone: ${analysis.tone}`);
		console.log(`Structure: ${analysis.structure}`);
		console.log(`Key Characteristics: ${analysis.keyCharacteristics}`);
		console.log(`Post Length: ${analysis.postLength}`);
		console.log(`Top Keywords: ${analysis.topKeywords.join(", ")}`);
		console.log(`Avg. Sentence Length: ${analysis.avgSentenceLength} words`);
		console.log(`Avg. Post Length: ${analysis.avgPostLength} words`);
		console.log(`Hashtag Usage: ${analysis.hashtagFrequency}`);
		console.log(`Emoji Usage: ${analysis.emojiFrequency}`);
	} catch (error) {
		console.error(
			"Error:",
			error instanceof Error ? error.message : "Unknown error"
		);
	}
}

const args = process.argv.slice(2);

// Check for environment variables first
const email = process.env.LINKEDIN_EMAIL;
const password = process.env.LINKEDIN_PASSWORD;

if (args.length < 1) {
	console.error("Usage: npm start <LinkedIn_Profile_URL> [Email] [Password]");
	console.error(
		"Example: npm start https://www.linkedin.com/in/johndoe <EMAIL> your-password"
	);
	console.error(
		"Or set LINKEDIN_EMAIL and LINKEDIN_PASSWORD environment variables"
	);
	process.exit(1);
}

// Use command line args or environment variables
const finalEmail = args[1] || email;
const finalPassword = args[2] || password;

if (!finalEmail || !finalPassword) {
	console.error("LinkedIn credentials are required!");
	console.error(
		"Provide them as command line arguments or set LINKEDIN_EMAIL and LINKEDIN_PASSWORD environment variables"
	);
	process.exit(1);
}

main(args[0], finalEmail, finalPassword);
