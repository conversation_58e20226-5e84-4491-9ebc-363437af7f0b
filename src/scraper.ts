// src/scraper.ts
import puppeteer, { Page } from "puppeteer";
import { Post } from "./types";

interface LinkedInCredentials {
	email: string;
	password: string;
}

export async function scrapeLinkedInPosts(
	profileUrl: string,
	credentials: LinkedInCredentials
): Promise<Post[]> {
	const browser = await puppeteer.launch({
		headless: false, // Set to false to see the browser for debugging login issues
		args: [
			"--no-sandbox",
			"--disable-setuid-sandbox",
			"--disable-blink-features=AutomationControlled",
			"--disable-features=VizDisplayCompositor",
		],
	});

	const page = await browser.newPage();

	// Set realistic viewport
	await page.setViewport({ width: 1366, height: 768 });

	// Set user agent
	await page.setUserAgent(
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
	);

	// Remove webdriver property to avoid detection
	await page.evaluateOnNewDocument(() => {
		Object.defineProperty(navigator, "webdriver", {
			get: () => undefined,
		});
	});

	try {
		// First, login to LinkedIn
		await loginToLinkedIn(page, credentials);

		// Then navigate to the profile's recent activity page
		console.log("Navigating to recent activity page...");
		await page.goto(`${profileUrl}/recent-activity/`, {
			waitUntil: "domcontentloaded",
			timeout: 60000,
		});

		// Wait for a specific selector that indicates page readiness
		await page.waitForSelector(".feed-shared-update-v2", { timeout: 30000 });

		// Scroll to load more posts
		await autoScroll(page);

		const posts = await page.evaluate(() => {
			const results: Post[] = [];
			const postElements = document.querySelectorAll(".feed-shared-update-v2");

			postElements.forEach((element) => {
				try {
					const textElement = element.querySelector(
						".update-components-text"
					) as HTMLElement;
					const dateElement = element.querySelector(
						".update-components-actor__sub-description"
					) as HTMLElement;
					const likesElement = element.querySelector(
						".social-details-social-counts__reactions-count"
					) as HTMLElement;
					const commentsElement = element.querySelector(
						".social-details-social-counts__comments"
					) as HTMLElement;

					const text = textElement?.innerText?.trim() || "";
					const date = dateElement?.innerText?.trim() || "";

					// Parse engagement numbers
					const likes = likesElement
						? parseInt(likesElement.innerText.replace(/,/g, ""))
						: null;
					const comments = commentsElement
						? parseInt(commentsElement.innerText.replace(/,/g, ""))
						: null;

					if (text) {
						results.push({ text, date, likes, comments });
					}
				} catch (e) {
					console.error("Error parsing post:", e);
				}
			});

			return results.slice(0, 15); // Return max 15 posts
		});

		return posts;
	} finally {
		await browser.close();
	}
}

// Helper function to login to LinkedIn
async function loginToLinkedIn(
	page: Page,
	credentials: LinkedInCredentials
): Promise<void> {
	console.log("Logging into LinkedIn...");

	// Navigate to LinkedIn login page
	await page.goto("https://www.linkedin.com/login", {
		waitUntil: "networkidle2",
		timeout: 60000,
	});

	// Wait for login form to be visible
	await page.waitForSelector("#username", { timeout: 10000 });
	await page.waitForSelector("#password", { timeout: 10000 });

	// Fill in credentials
	await page.type("#username", credentials.email);
	await page.type("#password", credentials.password);

	// Click login button
	await page.click('button[type="submit"]');

	// Wait for navigation after login
	// await page.waitForNavigation({
	// 	waitUntil: "networkidle2",
	// 	timeout: 50000
	// });

	// Check if we're successfully logged in by looking for the LinkedIn feed or profile
	try {
		await page.waitForSelector(".global-nav", { timeout: 30000 });
		console.log("Successfully logged into LinkedIn");
	} catch (error) {
		// Check if we need to handle 2FA or other verification
		const currentUrl = page.url();
		if (currentUrl.includes("challenge") || currentUrl.includes("checkpoint")) {
			console.log(
				"LinkedIn requires additional verification. Please check your email/phone for verification code."
			);
			// Wait for user to complete verification manually
			await page.waitForNavigation({
				waitUntil: "networkidle2",
				timeout: 120000, // Give 2 minutes for manual verification
			});
		} else {
			throw new Error(
				"Failed to login to LinkedIn. Please check your credentials."
			);
		}
	}
}

// Helper to auto-scroll through the page
async function autoScroll(page: Page) {
	await page.evaluate(async () => {
		await new Promise<void>((resolve) => {
			let totalHeight = 0;
			const distance = 500;
			const timer = setInterval(() => {
				const scrollHeight = document.body.scrollHeight;
				window.scrollBy(0, distance);
				totalHeight += distance;

				if (totalHeight >= scrollHeight - window.innerHeight) {
					clearInterval(timer);
					resolve();
				}
			}, 200);
		});
	});
}
