import { WritingStyleProfile, Post } from "./types";

export function analyzeWritingStyle(posts: Post[]): WritingStyleProfile {
	const allText = posts.map((p) => p.text).join(" ");

	// 1. Tone Analysis
	const tone = analyzeTone(allText);

	// 2. Structure Analysis
	const structure = analyzeStructure(posts);

	// 3. Key Characteristics
	const keyCharacteristics = identifyCharacteristics(allText);

	// 4. Post Length Analysis
	const postLength = analyzePostLength(posts);

	// 5. Keyword Analysis
	const topKeywords = getTopKeywords(allText, 5);

	// 6. Sentence Length
	const avgSentenceLength = calculateAvgSentenceLength(allText);

	// 7. Average Post Length
	const avgPostLength = calculateAvgPostLength(posts);

	// 8. Formatting Analysis
	const hashtagFrequency = analyzeHashtags(posts);
	const emojiFrequency = analyzeEmojis(posts);

	return {
		tone,
		structure,
		keyCharacteristics,
		postLength,
		topKeywords,
		avgSentenceLength,
		avgPostLength,
		hashtagFrequency,
		emojiFrequency,
	};
}

// Helper functions for analysis
function analyzeTone(text: string): string {
	const positiveWords = [
		"great",
		"excellent",
		"happy",
		"amazing",
		"love",
		"success",
		"win",
		"positive",
	];
	const negativeWords = [
		"challenge",
		"difficult",
		"hard",
		"issue",
		"problem",
		"concern",
		"negative",
	];
	const formalWords = [
		"however",
		"furthermore",
		"therefore",
		"additionally",
		"consequently",
	];

	const words = text.toLowerCase().split(/\s+/);
	let positiveCount = 0;
	let negativeCount = 0;
	let formalCount = 0;

	words.forEach((word) => {
		if (positiveWords.includes(word)) positiveCount++;
		if (negativeWords.includes(word)) negativeCount++;
		if (formalWords.includes(word)) formalCount++;
	});

	const sentiment =
		positiveCount > negativeCount
			? "mostly positive"
			: negativeCount > positiveCount
			? "mostly critical"
			: "neutral";

	const formality =
		formalCount > 10
			? "formal"
			: words.some((w) => w.includes("'"))
			? "casual"
			: "neutral";

	return `${sentiment}, ${formality}`;
}

function analyzeStructure(posts: Post[]): string {
	let bulletListCount = 0;
	let questionCount = 0;
	let hashtagCount = 0;

	posts.forEach((post) => {
		if (post.text.includes("•") || post.text.includes("- ")) bulletListCount++;
		if (post.text.includes("?")) questionCount++;
		if (post.text.includes("#")) hashtagCount++;
	});

	const features = [];
	if (bulletListCount > posts.length / 2) features.push("uses bullet points");
	if (questionCount > posts.length / 3) features.push("asks questions");
	if (hashtagCount > posts.length * 2) features.push("frequent hashtags");

	return features.length > 0
		? features.join(", ")
		: "standard paragraph structure";
}

function identifyCharacteristics(text: string): string {
	const characteristics = [];

	if ((text.match(/\?/g) || []).length > 5) {
		characteristics.push("question-driven");
	}

	if ((text.match(/!{2,}/g) || []).length > 3) {
		characteristics.push("emphatic expressions");
	}

	const urlRegex = /https?:\/\/[^\s]+/g;
	if ((text.match(urlRegex) || []).length > 2) {
		characteristics.push("resource sharing");
	}

	return characteristics.length > 0
		? characteristics.join(", ")
		: "direct messaging style";
}

function analyzePostLength(posts: Post[]): string {
	const wordCounts = posts.map((p) => p.text.split(/\s+/).length);
	const avgWords = wordCounts.reduce((a, b) => a + b, 0) / wordCounts.length;

	if (avgWords < 50) return "short-form (concise)";
	if (avgWords < 200) return "medium-length";
	return "long-form (detailed)";
}

function getTopKeywords(text: string, count: number): string[] {
	const words = text.toLowerCase().match(/\b\w+\b/g) || [];
	const stopWords = new Set([
		"the",
		"and",
		"to",
		"of",
		"a",
		"in",
		"is",
		"it",
		"that",
		"with",
		"for",
		"on",
		"as",
		"by",
		"at",
	]);

	const freq: Record<string, number> = {};
	words.forEach((word) => {
		if (!stopWords.has(word) && word.length > 3) {
			freq[word] = (freq[word] || 0) + 1;
		}
	});

	return Object.entries(freq)
		.sort((a, b) => b[1] - a[1])
		.slice(0, count)
		.map((entry) => entry[0]);
}

function calculateAvgSentenceLength(text: string): number {
	const sentences = text.split(/[.!?]+/).filter((s) => s.trim().length > 0);
	if (sentences.length === 0) return 0;

	const totalWords = sentences.reduce(
		(count, sentence) => count + sentence.trim().split(/\s+/).length,
		0
	);

	return Math.round(totalWords / sentences.length);
}

function calculateAvgPostLength(posts: Post[]): number {
	const totalWords = posts.reduce(
		(count, post) => count + post.text.split(/\s+/).length,
		0
	);

	return Math.round(totalWords / posts.length);
}

function analyzeHashtags(posts: Post[]): string {
	const hashtagCount = posts.reduce(
		(count, post) => count + (post.text.match(/#\w+/g)?.length || 0),
		0
	);

	const avgPerPost = hashtagCount / posts.length;
	if (avgPerPost > 3) return "heavy";
	if (avgPerPost > 1) return "moderate";
	return "light";
}

function analyzeEmojis(posts: Post[]): string {
	const emojiCount = posts.reduce(
		(count, post) =>
			count + (post.text.match(/[\u{1F600}-\u{1F6FF}]/gu)?.length || 0),
		0
	);

	const avgPerPost = emojiCount / posts.length;
	if (avgPerPost > 2) return "frequent";
	if (avgPerPost > 0.5) return "occasional";
	return "rare";
}
